{"RootPath": "C:\\Users\\<USER>\\Downloads\\大便\\RandomDeathMatch", "ProjectFileName": "Deathmatch.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Dependencies\\BadgeOverride.cs"}, {"SourceFile": "Dependencies\\BroadcastOverride.cs"}, {"SourceFile": "Dependencies\\FacilityManager.cs"}, {"SourceFile": "Dependencies\\HintOverride.cs"}, {"SourceFile": "Dependencies\\Teleport.cs"}, {"SourceFile": "Features\\AttachmentBlacklist.cs"}, {"SourceFile": "Features\\Cleanup.cs"}, {"SourceFile": "Features\\Database.cs"}, {"SourceFile": "Features\\Experience.cs"}, {"SourceFile": "Features\\IFeatures.cs"}, {"SourceFile": "Features\\LeaderBoard.cs"}, {"SourceFile": "Features\\Menus.cs"}, {"SourceFile": "Dependencies\\InventoryMenu.cs"}, {"SourceFile": "Features\\Killfeed.cs"}, {"SourceFile": "Features\\Killstreak.cs"}, {"SourceFile": "Features\\Loadout.cs"}, {"SourceFile": "Deathmatch.cs"}, {"SourceFile": "Features\\Rank.cs"}, {"SourceFile": "Features\\Rooms.cs"}, {"SourceFile": "Features\\Round.cs"}, {"SourceFile": "Features\\Tracking.cs"}, {"SourceFile": "Features\\TranslationConfig.cs"}, {"SourceFile": "Features\\VoiceChat.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Features\\Lobby.cs"}, {"SourceFile": "Features\\Stats.cs"}, {"SourceFile": "Dependencies\\Utility.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Downloads\\大便\\RandomDeathMatch\\bin\\Debug\\0Harmony.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\Assembly-CSharp-firstpass.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\publicized_assemblies\\Assembly-CSharp_publicized.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\CommandSystem.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Downloads\\大便\\RandomDeathMatch\\bin\\Debug\\Glicko2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Downloads\\大便\\packages\\Northwood.LabAPI.1.0.2\\lib\\net48\\LabApi.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Downloads\\大便\\RandomDeathMatch\\bin\\Debug\\LiteDB.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\Mirror.Components.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Downloads\\大便\\RandomDeathMatch\\bin\\Debug\\Mirror_publicized.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\NorthwoodLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\Pooling.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Downloads\\大便\\packages\\System.Buffers.4.5.1\\lib\\net461\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\Unity.Mathematics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\UnityEngine.CoreModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\UnityEngine.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\steam\\steamapps\\common\\SCP Secret Laboratory Dedicated Server\\SCPSL_Data\\Managed\\UnityEngine.PhysicsModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Downloads\\大便\\packages\\YamlDotNet.11.0.1\\lib\\net45\\YamlDotNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Downloads\\大便\\RandomDeathMatch\\bin\\Debug\\Deathmatch.dll", "OutputItemRelativePath": "Deathmatch.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}